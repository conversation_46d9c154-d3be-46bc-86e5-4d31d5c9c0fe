// Black Jack hra - JavaScript logika
class BlackJackGame {
    constructor() {
        this.deck = [];
        this.playerCards = [];
        this.dealerCards = [];
        this.playerScore = 0;
        this.dealerScore = 0;
        this.balance = 1000;
        this.currentBet = 0;
        this.gameState = 'betting'; // betting, playing, dealer-turn, game-over
        this.dealerHiddenCard = null;
        
        this.initializeElements();
        this.initializeEventListeners();
        this.updateDisplay();
    }

    initializeElements() {
        // DOM elementy
        this.balanceEl = document.getElementById('balance');
        this.currentBetEl = document.getElementById('current-bet');
        this.playerScoreEl = document.getElementById('player-score');
        this.dealerScoreEl = document.getElementById('dealer-score');
        this.playerCardsEl = document.getElementById('player-cards');
        this.dealerCardsEl = document.getElementById('dealer-cards');
        this.gameMessageEl = document.getElementById('game-message');
        
        // Ovládací prvky
        this.bettingControlsEl = document.getElementById('betting-controls');
        this.gameControlsEl = document.getElementById('game-controls');
        this.newGameControlsEl = document.getElementById('new-game-controls');
        
        // Tlačítka
        this.betButtons = document.querySelectorAll('.bet-btn');
        this.dealBtn = document.getElementById('deal-btn');
        this.hitBtn = document.getElementById('hit-btn');
        this.standBtn = document.getElementById('stand-btn');
        this.doubleBtn = document.getElementById('double-btn');
        this.newGameBtn = document.getElementById('new-game-btn');
        
        // Zvuk
        this.cardSound = document.getElementById('card-sound');
    }

    initializeEventListeners() {
        // Sázení
        this.betButtons.forEach(btn => {
            btn.addEventListener('click', () => this.placeBet(parseInt(btn.dataset.bet)));
        });
        
        this.dealBtn.addEventListener('click', () => this.startGame());
        
        // Herní akce
        this.hitBtn.addEventListener('click', () => this.hit());
        this.standBtn.addEventListener('click', () => this.stand());
        this.doubleBtn.addEventListener('click', () => this.doubleDown());
        this.newGameBtn.addEventListener('click', () => this.newGame());
    }

    // Vytvoření balíčku karet
    createDeck() {
        const suits = ['♠', '♥', '♦', '♣'];
        const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        
        this.deck = [];
        
        for (let suit of suits) {
            for (let value of values) {
                this.deck.push({
                    suit: suit,
                    value: value,
                    numericValue: this.getCardValue(value)
                });
            }
        }
        
        // Zamíchání balíčku
        this.shuffleDeck();
    }

    shuffleDeck() {
        for (let i = this.deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]];
        }
    }

    getCardValue(value) {
        if (value === 'A') return 11;
        if (['J', 'Q', 'K'].includes(value)) return 10;
        return parseInt(value);
    }

    // Výpočet skóre s ošetřením es
    calculateScore(cards) {
        let score = 0;
        let aces = 0;
        
        for (let card of cards) {
            if (card.value === 'A') {
                aces++;
            }
            score += card.numericValue;
        }
        
        // Ošetření es - změna z 11 na 1 pokud je skóre > 21
        while (score > 21 && aces > 0) {
            score -= 10;
            aces--;
        }
        
        return score;
    }

    // Sázení
    placeBet(amount) {
        if (amount > this.balance) {
            this.showMessage('Nemáte dostatek peněz!', 'lose');
            return;
        }
        
        this.currentBet = amount;
        this.updateDisplay();
        
        // Označení vybrané sázky
        this.betButtons.forEach(btn => btn.classList.remove('selected'));
        event.target.classList.add('selected');
        
        this.dealBtn.disabled = false;
    }

    // Začátek hry
    startGame() {
        if (this.currentBet === 0) return;
        
        this.balance -= this.currentBet;
        this.gameState = 'playing';
        
        this.createDeck();
        this.playerCards = [];
        this.dealerCards = [];
        
        // Rozdání počátečních karet
        this.dealCard('player');
        this.dealCard('dealer');
        this.dealCard('player');
        this.dealCard('dealer', true); // Druhá karta dealera je skrytá
        
        this.updateScores();
        this.updateDisplay();
        this.showControls('game');
        
        // Kontrola Black Jack
        if (this.playerScore === 21) {
            this.showMessage('BLACK JACK!', 'win');
            this.stand();
        }
    }

    // Rozdání karty
    dealCard(target, hidden = false) {
        if (this.deck.length === 0) return;
        
        const card = this.deck.pop();
        
        if (target === 'player') {
            this.playerCards.push(card);
            this.displayCard(card, this.playerCardsEl);
        } else {
            this.dealerCards.push(card);
            if (hidden) {
                this.dealerHiddenCard = card;
                this.displayCard(null, this.dealerCardsEl, true); // Zobrazit rubem
            } else {
                this.displayCard(card, this.dealerCardsEl);
            }
        }
        
        this.playCardSound();
    }

    // Zobrazení karty
    displayCard(card, container, isHidden = false) {
        const cardEl = document.createElement('div');
        cardEl.className = 'card new-card';
        
        if (isHidden) {
            cardEl.classList.add('card-back');
            cardEl.innerHTML = '🂠';
        } else {
            const isRed = ['♥', '♦'].includes(card.suit);
            cardEl.classList.add(isRed ? 'red' : 'black');
            
            cardEl.innerHTML = `
                <div class="card-value">${card.value}</div>
                <div class="card-suit">${card.suit}</div>
                <div class="card-value" style="transform: rotate(180deg);">${card.value}</div>
            `;
        }
        
        container.appendChild(cardEl);
    }

    // Aktualizace skóre
    updateScores() {
        this.playerScore = this.calculateScore(this.playerCards);
        
        // Pro dealera počítáme skóre bez skryté karty během hry
        let dealerVisibleCards = this.dealerCards.slice();
        if (this.dealerHiddenCard && this.gameState === 'playing') {
            dealerVisibleCards = dealerVisibleCards.slice(0, -1);
        }
        this.dealerScore = this.calculateScore(dealerVisibleCards);
    }

    // Herní akce
    hit() {
        this.dealCard('player');
        this.updateScores();
        this.updateDisplay();
        
        if (this.playerScore > 21) {
            this.showMessage('BUST! Prohráli jste!', 'lose');
            this.endGame();
        } else if (this.playerScore === 21) {
            this.stand();
        }
    }

    stand() {
        this.gameState = 'dealer-turn';
        this.revealDealerCard();
        this.dealerPlay();
    }

    doubleDown() {
        if (this.balance < this.currentBet) {
            this.showMessage('Nemáte dostatek peněz na zdvojení!', 'lose');
            return;
        }
        
        this.balance -= this.currentBet;
        this.currentBet *= 2;
        this.updateDisplay();
        
        this.dealCard('player');
        this.updateScores();
        this.updateDisplay();
        
        if (this.playerScore > 21) {
            this.showMessage('BUST! Prohráli jste!', 'lose');
            this.endGame();
        } else {
            this.stand();
        }
    }

    // Odkrytí skryté karty dealera
    revealDealerCard() {
        if (this.dealerHiddenCard) {
            // Odstranění skryté karty
            const hiddenCardEl = this.dealerCardsEl.querySelector('.card-back');
            if (hiddenCardEl) {
                hiddenCardEl.remove();
            }
            
            // Zobrazení skutečné karty
            this.displayCard(this.dealerHiddenCard, this.dealerCardsEl);
            this.dealerHiddenCard = null;
            
            this.updateScores();
            this.updateDisplay();
        }
    }

    // Hra dealera
    dealerPlay() {
        const dealerTurn = () => {
            this.dealerScore = this.calculateScore(this.dealerCards);
            
            if (this.dealerScore < 17) {
                setTimeout(() => {
                    this.dealCard('dealer');
                    this.updateScores();
                    this.updateDisplay();
                    dealerTurn();
                }, 1000);
            } else {
                this.determineWinner();
            }
        };
        
        dealerTurn();
    }

    // Určení vítěze
    determineWinner() {
        let message = '';
        let messageType = '';
        
        if (this.dealerScore > 21) {
            message = 'Dealer má BUST! Vyhráli jste!';
            messageType = 'win';
            this.balance += this.currentBet * 2;
        } else if (this.playerScore > this.dealerScore) {
            message = 'Vyhráli jste!';
            messageType = 'win';
            this.balance += this.currentBet * 2;
        } else if (this.playerScore < this.dealerScore) {
            message = 'Prohráli jste!';
            messageType = 'lose';
        } else {
            message = 'Remíza!';
            messageType = '';
            this.balance += this.currentBet; // Vrácení sázky
        }
        
        this.showMessage(message, messageType);
        this.endGame();
    }

    // Konec hry
    endGame() {
        this.gameState = 'game-over';
        this.showControls('new-game');
        this.updateDisplay();
        
        if (this.balance === 0) {
            setTimeout(() => {
                this.showMessage('Došly vám peníze! Hra se restartuje.', 'lose');
                setTimeout(() => this.resetGame(), 2000);
            }, 2000);
        }
    }

    // Nová hra
    newGame() {
        this.gameState = 'betting';
        this.currentBet = 0;
        this.playerCards = [];
        this.dealerCards = [];
        this.dealerHiddenCard = null;
        this.playerScore = 0;
        this.dealerScore = 0;
        
        this.playerCardsEl.innerHTML = '';
        this.dealerCardsEl.innerHTML = '';
        this.gameMessageEl.innerHTML = '';
        
        this.betButtons.forEach(btn => btn.classList.remove('selected'));
        this.dealBtn.disabled = true;
        
        this.showControls('betting');
        this.updateDisplay();
    }

    // Reset celé hry
    resetGame() {
        this.balance = 1000;
        this.newGame();
    }

    // Zobrazení ovládacích prvků
    showControls(type) {
        this.bettingControlsEl.style.display = type === 'betting' ? 'block' : 'none';
        this.gameControlsEl.style.display = type === 'game' ? 'block' : 'none';
        this.newGameControlsEl.style.display = type === 'new-game' ? 'block' : 'none';
    }

    // Zobrazení zprávy
    showMessage(text, type = '') {
        this.gameMessageEl.textContent = text;
        this.gameMessageEl.className = `game-message ${type}`;
    }

    // Přehrání zvuku karty
    playCardSound() {
        if (this.cardSound) {
            this.cardSound.currentTime = 0;
            this.cardSound.play().catch(() => {
                // Ignorovat chyby přehrávání zvuku
            });
        }
    }

    // Aktualizace zobrazení
    updateDisplay() {
        this.balanceEl.textContent = this.balance;
        this.currentBetEl.textContent = this.currentBet;
        this.playerScoreEl.textContent = this.playerScore;
        this.dealerScoreEl.textContent = this.dealerScore;
        
        // Zakázání tlačítek podle stavu hry
        if (this.gameState === 'playing') {
            this.doubleBtn.disabled = this.balance < this.currentBet || this.playerCards.length > 2;
        }
    }
}

// Spuštění hry po načtení stránky
document.addEventListener('DOMContentLoaded', () => {
    new BlackJackGame();
});
