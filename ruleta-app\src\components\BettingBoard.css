.betting-board {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 15px;
  padding: 20px;
  border: 2px solid #FFD700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.numbers-grid {
  display: grid;
  grid-template-columns: 60px 1fr 60px;
  gap: 2px;
  margin-bottom: 10px;
}

.zero {
  grid-row: 1 / 13;
  writing-mode: vertical-lr;
  text-orientation: mixed;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #228B22, #006400) !important;
}

.main-numbers {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 2px;
}

.main-numbers .number-cell:nth-child(3n-2) {
  grid-row: 3;
}

.main-numbers .number-cell:nth-child(3n-1) {
  grid-row: 2;
}

.main-numbers .number-cell:nth-child(3n) {
  grid-row: 1;
}

.columns {
  display: grid;
  grid-template-rows: repeat(3, 1fr);
  gap: 2px;
}

.number-cell {
  position: relative;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px solid #FFD700;
  border-radius: 8px;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  transition: all 0.3s ease;
  user-select: none;
}

.number-cell:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
}

.number-cell.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
}

.number-cell.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
}

.number-cell.green {
  background: linear-gradient(45deg, #228B22, #006400);
}

.number-cell.has-bet {
  border-color: #00FF00;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.bet-chip {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #FFD700;
  color: #000;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  border: 2px solid #FFA500;
}

.dozens {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin: 10px 0;
}

.dozen-bet, .column-bet {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #4a90e2, #357abd);
  border: 2px solid #FFD700;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  color: white;
  transition: all 0.3s ease;
  user-select: none;
}

.dozen-bet:hover, .column-bet:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
}

.dozen-bet.has-bet, .column-bet.has-bet {
  border-color: #00FF00;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.outside-bets {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
}

.outside-bet {
  position: relative;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #FFD700;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  color: white;
  transition: all 0.3s ease;
  user-select: none;
  background: linear-gradient(45deg, #666, #444);
}

.outside-bet:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
}

.outside-bet.has-bet {
  border-color: #00FF00;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.outside-bet.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
}

.outside-bet.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
}

.outside-bet span {
  font-size: 12px;
  text-align: center;
}

@media (max-width: 768px) {
  .betting-board {
    padding: 10px;
  }
  
  .number-cell {
    width: 35px;
    height: 35px;
    font-size: 12px;
  }
  
  .outside-bet {
    height: 40px;
  }
  
  .outside-bet span {
    font-size: 10px;
  }
  
  .dozen-bet, .column-bet {
    height: 35px;
    font-size: 12px;
  }
}
