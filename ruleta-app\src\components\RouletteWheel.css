.roulette-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.roulette-wheel-wrapper {
  position: relative;
  width: 400px;
  height: 400px;
  margin: 0 auto;
}

.roulette-wheel {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  background:
    radial-gradient(circle at 50% 50%, #2d1810 0%, #1a0f08 30%),
    conic-gradient(from 0deg,
      #8B4513 0deg, #654321 10deg, #8B4513 20deg, #654321 30deg,
      #8B4513 40deg, #654321 50deg, #8B4513 60deg, #654321 70deg,
      #8B4513 80deg, #654321 90deg, #8B4513 100deg, #654321 110deg,
      #8B4513 120deg, #654321 130deg, #8B4513 140deg, #654321 150deg,
      #8B4513 160deg, #654321 170deg, #8B4513 180deg, #654321 190deg,
      #8B4513 200deg, #654321 210deg, #8B4513 220deg, #654321 230deg,
      #8B4513 240deg, #654321 250deg, #8B4513 260deg, #654321 270deg,
      #8B4513 280deg, #654321 290deg, #8B4513 300deg, #654321 310deg,
      #8B4513 320deg, #654321 330deg, #8B4513 340deg, #654321 350deg,
      #8B4513 360deg);
  border: 12px solid #FFD700;
  box-shadow:
    0 0 40px rgba(255, 215, 0, 0.8),
    inset 0 0 30px rgba(0, 0, 0, 0.5),
    0 0 80px rgba(255, 215, 0, 0.3);
  transition: transform 4s cubic-bezier(0.23, 1, 0.32, 1);
}

.roulette-wheel.spinning {
  transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
}

.wheel-number {
  position: absolute;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
  border: 3px solid #FFD700;
  top: 50%;
  left: 50%;
  margin-left: -17.5px;
  margin-top: -17.5px;
  transform-origin: 17.5px 170px;
  box-shadow:
    0 0 10px rgba(255, 215, 0, 0.5),
    inset 0 0 5px rgba(0, 0, 0, 0.3);
}

.wheel-number.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
}

.wheel-number.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
}

.wheel-number.green {
  background: linear-gradient(45deg, #228B22, #006400);
}

.wheel-pointer {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 40px solid #FFD700;
  z-index: 15;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
}

.wheel-pointer::after {
  content: '';
  position: absolute;
  top: -35px;
  left: -15px;
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 30px solid #FFA500;
}

.wheel-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background:
    radial-gradient(circle at 30% 30%, #FFD700 0%, #FFA500 50%, #FF8C00 100%);
  border: 5px solid #8B4513;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow:
    0 0 25px rgba(255, 215, 0, 0.8),
    inset 0 0 15px rgba(255, 255, 255, 0.3);
}

.center-circle {
  text-align: center;
}

.center-circle span {
  font-size: 14px;
  font-weight: bold;
  color: #8B4513;
  text-shadow:
    1px 1px 2px rgba(255, 255, 255, 0.8),
    0 0 5px rgba(255, 215, 0, 0.5);
  letter-spacing: 1px;
}

.result-announcement {
  text-align: center;
  animation: fadeInScale 0.5s ease-out;
}

.result-number {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
  margin: 0 auto 10px;
  border: 3px solid #FFD700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.result-number.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
}

.result-number.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
}

.result-number.green {
  background: linear-gradient(45deg, #228B22, #006400);
}

.result-text {
  font-size: 1.2rem;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@media (max-width: 768px) {
  .roulette-wheel-wrapper {
    width: 250px;
    height: 250px;
  }

  .wheel-number {
    width: 20px;
    height: 20px;
    font-size: 10px;
    margin-left: -10px;
    margin-top: -10px;
    transform-origin: 10px 115px;
  }

  .wheel-center {
    width: 60px;
    height: 60px;
  }

  .center-circle span {
    font-size: 8px;
  }
}
