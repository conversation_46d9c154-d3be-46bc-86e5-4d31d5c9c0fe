.roulette-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.roulette-wheel-wrapper {
  position: relative;
  width: 300px;
  height: 300px;
}

.roulette-wheel {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  background: radial-gradient(circle, #8B4513 0%, #654321 50%, #3E2723 100%);
  border: 8px solid #FFD700;
  box-shadow: 
    0 0 20px rgba(255, 215, 0, 0.5),
    inset 0 0 20px rgba(0, 0, 0, 0.3);
  transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
}

.roulette-wheel.spinning {
  transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
}

.wheel-number {
  position: absolute;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  border: 2px solid #FFD700;
  top: 50%;
  left: 50%;
  margin-left: -12.5px;
  margin-top: -12.5px;
  transform-origin: 12.5px 140px;
}

.wheel-number.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
}

.wheel-number.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
}

.wheel-number.green {
  background: linear-gradient(45deg, #228B22, #006400);
}

.wheel-pointer {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 30px solid #FFD700;
  z-index: 10;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.wheel-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: radial-gradient(circle, #FFD700 0%, #FFA500 100%);
  border: 3px solid #8B4513;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
}

.center-circle {
  text-align: center;
}

.center-circle span {
  font-size: 10px;
  font-weight: bold;
  color: #8B4513;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.result-announcement {
  text-align: center;
  animation: fadeInScale 0.5s ease-out;
}

.result-number {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: white;
  margin: 0 auto 10px;
  border: 3px solid #FFD700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.result-number.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
}

.result-number.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
}

.result-number.green {
  background: linear-gradient(45deg, #228B22, #006400);
}

.result-text {
  font-size: 1.2rem;
  font-weight: bold;
  color: #FFD700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@media (max-width: 768px) {
  .roulette-wheel-wrapper {
    width: 250px;
    height: 250px;
  }
  
  .wheel-number {
    width: 20px;
    height: 20px;
    font-size: 10px;
    margin-left: -10px;
    margin-top: -10px;
    transform-origin: 10px 115px;
  }
  
  .wheel-center {
    width: 60px;
    height: 60px;
  }
  
  .center-circle span {
    font-size: 8px;
  }
}
