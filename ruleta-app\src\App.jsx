import React, { useState, useCallback } from 'react'
import RouletteWheel from './components/RouletteWheel'
import BettingBoard from './components/BettingBoard'
import StrategyPanel from './components/StrategyPanel'
import GameStats from './components/GameStats'

function App() {
  const [balance, setBalance] = useState(10000)
  const [currentBet, setCurrentBet] = useState(100)
  const [bets, setBets] = useState({})
  const [lastResult, setLastResult] = useState(null)
  const [isSpinning, setIsSpinning] = useState(false)
  const [gameHistory, setGameHistory] = useState([])
  const [totalWins, setTotalWins] = useState(0)
  const [totalLosses, setTotalLosses] = useState(0)

  const placeBet = useCallback((betType, amount) => {
    if (balance >= amount) {
      setBets(prev => ({
        ...prev,
        [betType]: (prev[betType] || 0) + amount
      }))
      setBalance(prev => prev - amount)
    }
  }, [balance])

  const clearBets = useCallback(() => {
    const totalBetAmount = Object.values(bets).reduce((sum, bet) => sum + bet, 0)
    setBalance(prev => prev + totalBetAmount)
    setBets({})
  }, [bets])

  const spinWheel = useCallback(() => {
    if (Object.keys(bets).length === 0) {
      alert('Musíte nejdříve vsadit!')
      return
    }

    setIsSpinning(true)
    
    // Simulace točení - náhodné číslo 0-36
    setTimeout(() => {
      const result = Math.floor(Math.random() * 37)
      setLastResult(result)
      
      // Výpočet výher
      let totalWinnings = 0
      const winningBets = []

      Object.entries(bets).forEach(([betType, betAmount]) => {
        const winAmount = calculateWinnings(betType, result, betAmount)
        if (winAmount > 0) {
          totalWinnings += winAmount
          winningBets.push({ type: betType, amount: winAmount })
        }
      })

      setBalance(prev => prev + totalWinnings)
      
      // Aktualizace statistik
      if (totalWinnings > 0) {
        setTotalWins(prev => prev + 1)
      } else {
        setTotalLosses(prev => prev + 1)
      }

      // Přidání do historie
      setGameHistory(prev => [{
        number: result,
        bets: { ...bets },
        winnings: totalWinnings,
        timestamp: new Date()
      }, ...prev.slice(0, 9)]) // Uchování posledních 10 her

      setBets({})
      setIsSpinning(false)
    }, 3000)
  }, [bets])

  const calculateWinnings = (betType, result, betAmount) => {
    const isRed = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(result)
    const isBlack = result !== 0 && !isRed
    const isEven = result !== 0 && result % 2 === 0
    const isOdd = result !== 0 && result % 2 === 1

    switch (betType) {
      case `number-${result}`:
        return betAmount * 36 // 35:1 + původní sázka
      case 'red':
        return isRed ? betAmount * 2 : 0
      case 'black':
        return isBlack ? betAmount * 2 : 0
      case 'even':
        return isEven ? betAmount * 2 : 0
      case 'odd':
        return isOdd ? betAmount * 2 : 0
      case 'low':
        return (result >= 1 && result <= 18) ? betAmount * 2 : 0
      case 'high':
        return (result >= 19 && result <= 36) ? betAmount * 2 : 0
      case 'dozen1':
        return (result >= 1 && result <= 12) ? betAmount * 3 : 0
      case 'dozen2':
        return (result >= 13 && result <= 24) ? betAmount * 3 : 0
      case 'dozen3':
        return (result >= 25 && result <= 36) ? betAmount * 3 : 0
      case 'column1':
        return [1,4,7,10,13,16,19,22,25,28,31,34].includes(result) ? betAmount * 3 : 0
      case 'column2':
        return [2,5,8,11,14,17,20,23,26,29,32,35].includes(result) ? betAmount * 3 : 0
      case 'column3':
        return [3,6,9,12,15,18,21,24,27,30,33,36].includes(result) ? betAmount * 3 : 0
      default:
        return 0
    }
  }

  const applyStrategy = useCallback((strategyBets) => {
    clearBets()
    let totalBetAmount = 0
    
    strategyBets.forEach(({ type, amount }) => {
      if (balance >= totalBetAmount + amount) {
        totalBetAmount += amount
      }
    })

    if (balance >= totalBetAmount) {
      const newBets = {}
      strategyBets.forEach(({ type, amount }) => {
        if (balance >= totalBetAmount) {
          newBets[type] = amount
        }
      })
      setBets(newBets)
      setBalance(prev => prev - totalBetAmount)
    } else {
      alert('Nedostatek prostředků pro tuto strategii!')
    }
  }, [balance, clearBets])

  return (
    <div className="app">
      <header className="header">
        <h1>🎰 Ruleta Casino 🎰</h1>
        <p>Inspirováno chance.cz - Vaše štěstí čeká!</p>
      </header>

      <div className="game-container">
        <div className="roulette-section">
          <RouletteWheel 
            isSpinning={isSpinning}
            lastResult={lastResult}
          />
          
          <div className="controls">
            <div className="input-group">
              <label>Sázka:</label>
              <input 
                type="number" 
                value={currentBet}
                onChange={(e) => setCurrentBet(Math.max(1, parseInt(e.target.value) || 1))}
                min="1"
                max={balance}
              />
              <span>Kč</span>
            </div>
            
            <button 
              className="btn btn-primary"
              onClick={spinWheel}
              disabled={isSpinning || Object.keys(bets).length === 0}
            >
              {isSpinning ? 'Točí se...' : 'TOČIT!'}
            </button>
            
            <button 
              className="btn btn-secondary"
              onClick={clearBets}
              disabled={isSpinning}
            >
              Vymazat sázky
            </button>
          </div>

          <BettingBoard 
            onPlaceBet={placeBet}
            currentBet={currentBet}
            bets={bets}
            disabled={isSpinning}
          />
        </div>

        <div className="side-panel">
          <div className="balance-display">
            <strong>Zůstatek: </strong>
            <span className={balance >= 10000 ? 'balance-positive' : 'balance-negative'}>
              {balance.toLocaleString()} Kč
            </span>
          </div>

          {lastResult !== null && (
            <div className="result-display">
              <div>Poslední číslo:</div>
              <div className="winning-number">{lastResult}</div>
            </div>
          )}

          <StrategyPanel 
            onApplyStrategy={applyStrategy}
            currentBet={currentBet}
            disabled={isSpinning}
          />

          <GameStats 
            totalWins={totalWins}
            totalLosses={totalLosses}
            gameHistory={gameHistory}
            balance={balance}
            initialBalance={10000}
          />
        </div>
      </div>
    </div>
  )
}

export default App
