# 🃏 Black Jack - <PERSON><PERSON> hra

Kompletní implementace klasické karetní hry Black <PERSON> (21) v HTML, CSS a JavaScript.

## 🎮 Funkce hry

### <PERSON><PERSON><PERSON><PERSON><PERSON> pravidla
- Cílem je dosáhnout co nejblíže k 21 bodům, an<PERSON><PERSON> byste je přek<PERSON>ili
- Karty 2-10 maj<PERSON> hodnotu podle čísla
- <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> = 10 bodů
- Eso = 11 bodů (nebo 1, pokud by 11 způsobilo překročení 21)

### Hern<PERSON> funkce
- **Sázení**: Vyberte si sázku před každou hrou (10, 25, 50, 100 Kč)
- **Hit**: Vezměte si další kartu
- **Stand**: Zůstaňte s aktuálními kartami
- **Double Down**: Zdvojte sázku a vezměte si pouze jednu kartu
- **Automatický dealer**: Dealer si bere karty podle pravidel (mus<PERSON> br<PERSON>t do 16, mus<PERSON> na 17+)

### Vizu<PERSON><PERSON><PERSON> prvky
- **Animovan<PERSON> karty**: <PERSON><PERSON><PERSON> se animo<PERSON>ě rozdávají
- **Responzivní design**: Hra funguje na všech zařízeních
- **Zvukové efekty**: Zvuk při rozdávání karet
- **Barevné rozlišení**: Červené a černé karty
- **Skrytá karta dealera**: Druhá karta dealera je skrytá až do konce

### Správa peněz
- Začínáte s 1000 Kč
- Vyhrajete-li, dostanete dvojnásobek sázky
- Při remíze se sázka vrací
- Hra se automaticky restartuje, když dojdou peníze

## 🚀 Spuštění hry

1. Otevřete soubor `index.html` ve webovém prohlížeči
2. Vyberte sázku kliknutím na jedno z tlačítek
3. Klikněte na "Rozdat karty"
4. Používejte tlačítka Hit, Stand nebo Double Down
5. Po skončení kola klikněte na "Nová hra"

## 🎯 Herní strategie

### Základní strategie
- **Hit** když máte 11 nebo méně (nemůžete překročit)
- **Stand** když máte 17 nebo více
- **Double Down** když máte 10 nebo 11 a dealer má slabou kartu (2-6)

### Pokročilé tipy
- Sledujte dealerovu viditelnou kartu
- Eso a 10 = Black Jack (automatická výhra, pokud dealer nemá také Black Jack)
- Buďte opatrní s esem - může být 1 nebo 11

## 🛠️ Technické detaily

### Struktura souborů
- `index.html` - HTML struktura hry
- `style.css` - CSS styly a animace
- `script.js` - JavaScript logika hry
- `README.md` - Dokumentace

### Použité technologie
- **HTML5** - Sémantická struktura
- **CSS3** - Moderní styly, animace, responzivní design
- **JavaScript ES6+** - Objektově orientovaná logika hry
- **Web Audio API** - Zvukové efekty

### Klíčové třídy a metody
- `BlackJackGame` - Hlavní třída hry
- `createDeck()` - Vytvoření a zamíchání balíčku
- `calculateScore()` - Výpočet skóre s ošetřením es
- `dealerPlay()` - Automatická hra dealera
- `determineWinner()` - Vyhodnocení vítěze

## 🎨 Customizace

### Změna vzhledu
- Upravte CSS proměnné v `style.css`
- Změňte barvy, fonty nebo animace
- Přidejte vlastní obrázky karet

### Rozšíření funkcí
- Přidání více balíčků karet
- Implementace pojištění (Insurance)
- Statistiky her
- Uložení high score do localStorage

## 📱 Kompatibilita

Hra je testována a funguje v:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobilní prohlížeče (iOS Safari, Chrome Mobile)

## 🐛 Známé problémy

- Zvukové efekty nemusí fungovat na některých mobilních zařízeních kvůli autoplay politikám
- Na velmi starých prohlížečích mohou chybět některé CSS animace

## 📄 Licence

Tento projekt je volně dostupný pro osobní i komerční použití.

---

**Užijte si hru a hodně štěstí! 🍀**
