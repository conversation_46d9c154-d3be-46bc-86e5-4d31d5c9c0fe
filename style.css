/* Reset a základní styly */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #0f4c3a, #2d8659);
    color: white;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-container {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    max-width: 1200px;
    width: 95%;
    min-height: 80vh;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #ffd700;
    padding-bottom: 20px;
}

header h1 {
    font-size: 3rem;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 15px;
}

.game-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    font-size: 1.2rem;
}

.balance, .bet {
    background: rgba(255, 215, 0, 0.1);
    padding: 10px 20px;
    border-radius: 10px;
    border: 1px solid #ffd700;
}

/* Hern<PERSON> deska */
.game-board {
    display: grid;
    grid-template-rows: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
    min-height: 400px;
}

.dealer-section, .player-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.dealer-section h2, .player-section h2 {
    color: #ffd700;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.score {
    font-size: 1.3rem;
    margin-bottom: 20px;
    font-weight: bold;
}

/* Karty */
.cards-container {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    min-height: 120px;
    align-items: center;
}

.card {
    width: 80px;
    height: 120px;
    background: white;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    position: relative;
    color: black;
    font-weight: bold;
}

.card:hover {
    transform: translateY(-5px);
}

.card.red {
    color: #d32f2f;
}

.card.black {
    color: #000;
}

.card-back {
    background: linear-gradient(45deg, #1a237e, #3949ab);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2rem;
}

.card-value {
    font-size: 1.1rem;
    font-weight: bold;
}

.card-suit {
    font-size: 1.5rem;
}

/* Animace pro nové karty */
.card.new-card {
    animation: dealCard 0.5s ease-out;
}

@keyframes dealCard {
    from {
        transform: translateY(-100px) rotate(180deg);
        opacity: 0;
    }
    to {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
}

/* Ovládací prvky */
.controls {
    text-align: center;
    margin-bottom: 20px;
}

.betting-controls h3 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.bet-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.bet-btn {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #000;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bet-btn:hover {
    background: linear-gradient(45deg, #ffed4e, #ffd700);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
}

.bet-btn.selected {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
}

.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.action-btn {
    background: linear-gradient(45deg, #4caf50, #66bb6a);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
}

.action-btn:hover:not(:disabled) {
    background: linear-gradient(45deg, #66bb6a, #4caf50);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
}

.action-btn:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
}

.action-btn.primary {
    background: linear-gradient(45deg, #2196f3, #42a5f5);
}

.action-btn.primary:hover:not(:disabled) {
    background: linear-gradient(45deg, #42a5f5, #2196f3);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

/* Zprávy */
.message-area {
    text-align: center;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-message {
    font-size: 1.4rem;
    font-weight: bold;
    padding: 15px 30px;
    border-radius: 10px;
    background: rgba(255, 215, 0, 0.1);
    border: 2px solid #ffd700;
    color: #ffd700;
    animation: messageAppear 0.5s ease-out;
}

@keyframes messageAppear {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.game-message.win {
    background: rgba(76, 175, 80, 0.2);
    border-color: #4caf50;
    color: #4caf50;
}

.game-message.lose {
    background: rgba(244, 67, 54, 0.2);
    border-color: #f44336;
    color: #f44336;
}

/* Responzivní design */
@media (max-width: 768px) {
    .game-container {
        padding: 20px;
        width: 98%;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .game-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .card {
        width: 60px;
        height: 90px;
        padding: 5px;
    }
    
    .card-value {
        font-size: 0.9rem;
    }
    
    .card-suit {
        font-size: 1.2rem;
    }
    
    .bet-buttons, .game-controls {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 200px;
    }
}
